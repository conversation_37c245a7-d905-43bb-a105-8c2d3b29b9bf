# OpenAI Proxy 日志管理

本文档描述了 OpenAI Proxy 服务的日志配置和管理功能。

## 日志目录

服务会按以下优先级选择日志目录：

1. **环境变量指定**: `OPENAI_PROXY_LOG_DIR` 环境变量指定的目录
2. **系统日志目录**: `/var/log/openai_proxy` (推荐用于生产环境)
3. **临时目录**: `/tmp/openai_proxy_logs` (非 root 用户的备选)
4. **用户主目录**: `~/.openai_proxy/logs` (用户级别的备选)
5. **当前目录**: 最后的备选方案

## 日志文件

- **主日志文件**: `openai_proxy.log` - 包含服务的所有日志信息
- **PID 文件**: `openai_proxy.pid` - 包含后台服务的进程 ID

## 使用方法

### 启动服务

```bash
# 前台运行（日志输出到控制台和文件）
proxy

# 后台运行（日志仅输出到文件）
proxy --background
```

### 服务管理

使用 `proxy-manage` 命令管理服务：

```bash
# 启动服务
proxy-manage start

# 停止服务
proxy-manage stop

# 重启服务
proxy-manage restart

# 查看服务状态
proxy-manage status

# 查看日志（最近50行）
proxy-manage logs

# 实时跟踪日志
proxy-manage logs -f
```

### Docker 环境

#### 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 直接使用 Docker

```bash
# 构建镜像
docker build -t openai-proxy .

# 运行容器（挂载日志目录）
docker run -d \
  --name openai-proxy \
  -p 8500:8500 \
  -v $(pwd)/logs:/var/log/openai_proxy \
  openai-proxy

# 查看日志
docker logs -f openai-proxy

# 或者查看挂载的日志文件
tail -f logs/openai_proxy.log
```

## 日志轮转

服务包含了 logrotate 配置，会自动进行日志轮转：

- **轮转频率**: 每天
- **保留天数**: 7 天
- **压缩**: 延迟压缩（第二天压缩）
- **权限**: 644 (root:root)

### 手动触发日志轮转

```bash
# 在容器内执行
logrotate -f /etc/logrotate.d/openai_proxy
```

## 故障排除

### 后台服务找不到日志文件

**问题**: 使用 `proxy --background` 后找不到日志文件

**可能原因**:
1. 日志目录没有写权限
2. 环境变量 `OPENAI_PROXY_LOG_DIR` 指向了不存在的目录
3. 服务启动失败但没有报错

**解决方案**:
1. 检查日志目录权限：
   ```bash
   ls -la /var/log/openai_proxy/
   ```

2. 检查服务状态：
   ```bash
   proxy-manage status
   ```

3. 查看启动日志：
   ```bash
   # 如果使用 systemd
   journalctl -u openai-proxy -f
   
   # 或者检查系统日志
   tail -f /var/log/syslog | grep openai
   ```

4. 手动指定日志目录：
   ```bash
   export OPENAI_PROXY_LOG_DIR=/tmp/openai_proxy_logs
   proxy --background
   ```

### 容器中的日志访问

**问题**: 容器运行后无法访问日志

**解决方案**:
1. 确保挂载了日志目录：
   ```bash
   docker run -v $(pwd)/logs:/var/log/openai_proxy ...
   ```

2. 检查容器内的日志：
   ```bash
   docker exec -it <container_name> ls -la /var/log/openai_proxy/
   ```

3. 使用 docker logs 查看容器日志：
   ```bash
   docker logs -f <container_name>
   ```

### 权限问题

**问题**: 权限不足无法写入日志

**解决方案**:
1. 确保日志目录有正确的权限：
   ```bash
   sudo mkdir -p /var/log/openai_proxy
   sudo chown $USER:$USER /var/log/openai_proxy
   sudo chmod 755 /var/log/openai_proxy
   ```

2. 或者使用用户目录：
   ```bash
   export OPENAI_PROXY_LOG_DIR=$HOME/.openai_proxy/logs
   ```

## 日志格式

日志使用标准的 Python logging 格式：

```
2024-01-15 10:30:45,123 - openai_proxy_msft.launcher - INFO - Service started with PID: 12345
2024-01-15 10:30:45,124 - openai_proxy_msft.proxy - INFO - Initialized renderer: chat_ml
```

格式说明：
- 时间戳
- 模块名称
- 日志级别
- 日志消息

## 监控建议

1. **磁盘空间监控**: 定期检查日志目录的磁盘使用情况
2. **日志级别**: 生产环境建议使用 INFO 级别
3. **日志聚合**: 考虑使用 ELK Stack 或类似工具进行日志聚合
4. **告警**: 设置关键错误的告警机制
